@echo off
REM Windows Batch Script for Document Compilation
REM This script compiles both LaTeX and Word versions of the conference paper

echo ========================================
echo Conference Paper Document Compilation
echo ========================================
echo.

REM Check if LaTeX is available
where pdflatex >nul 2>nul
if %errorlevel% neq 0 (
    echo Warning: pdflatex not found. LaTeX compilation will be skipped.
    echo Install MiKTeX or TeX Live to enable LaTeX compilation.
    set LATEX_AVAILABLE=0
) else (
    echo ✓ LaTeX found
    set LATEX_AVAILABLE=1
)

REM Check if pandoc is available
where pandoc >nul 2>nul
if %errorlevel% neq 0 (
    echo Warning: pandoc not found. Word conversion will be skipped.
    echo Install pandoc from https://pandoc.org to enable Word conversion.
    set PANDOC_AVAILABLE=0
) else (
    echo ✓ Pandoc found
    set PANDOC_AVAILABLE=1
)

echo.

REM LaTeX Compilation
if %LATEX_AVAILABLE%==1 (
    echo ========================================
    echo Compiling LaTeX Document
    echo ========================================
    
    if not exist "conference_paper.tex" (
        echo Error: conference_paper.tex not found!
        goto :word_conversion
    )
    
    if not exist "references.bib" (
        echo Error: references.bib not found!
        goto :word_conversion
    )
    
    echo Cleaning previous files...
    del /q *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fls *.fdb_latexmk *.synctex.gz 2>nul
    
    echo First LaTeX compilation...
    pdflatex conference_paper.tex
    
    echo Compiling bibliography...
    bibtex conference_paper
    
    echo Second LaTeX compilation...
    pdflatex conference_paper.tex
    
    echo Third LaTeX compilation...
    pdflatex conference_paper.tex
    
    if exist "conference_paper.pdf" (
        echo ✓ PDF compilation successful!
        echo Output: conference_paper.pdf
        
        REM Get file size
        for %%A in (conference_paper.pdf) do echo File size: %%~zA bytes
    ) else (
        echo ✗ PDF compilation failed!
        echo Check conference_paper.log for errors
    )
    
    echo.
) else (
    echo Skipping LaTeX compilation (pdflatex not available)
    echo.
)

:word_conversion
REM Word Document Conversion
if %PANDOC_AVAILABLE%==1 (
    echo ========================================
    echo Converting to Word Document
    echo ========================================
    
    if not exist "conference_paper_word.md" (
        echo Error: conference_paper_word.md not found!
        goto :cleanup
    )
    
    echo Converting markdown to Word...
    pandoc conference_paper_word.md -o "Conference_Paper_Rural_Development.docx" --from markdown --to docx --toc --number-sections --metadata title="Integrated Energy-Water-Health-Waste Management for Sustainable Rural Development"
    
    if exist "Conference_Paper_Rural_Development.docx" (
        echo ✓ Word conversion successful!
        echo Output: Conference_Paper_Rural_Development.docx
        
        REM Get file size
        for %%A in (Conference_Paper_Rural_Development.docx) do echo File size: %%~zA bytes
    ) else (
        echo ✗ Word conversion failed!
    )
    
    echo.
) else (
    echo Skipping Word conversion (pandoc not available)
    echo.
)

:cleanup
echo ========================================
echo Compilation Summary
echo ========================================

if exist "conference_paper.pdf" (
    echo ✓ LaTeX PDF: conference_paper.pdf
) else (
    echo ✗ LaTeX PDF: Not created
)

if exist "Conference_Paper_Rural_Development.docx" (
    echo ✓ Word Document: Conference_Paper_Rural_Development.docx
) else (
    echo ✗ Word Document: Not created
)

echo.
echo Files created in current directory:
dir /b *.pdf *.docx 2>nul

echo.
echo ========================================
echo Manual Steps Required
echo ========================================
echo.
echo For Word Document:
echo 1. Open Conference_Paper_Rural_Development.docx in Microsoft Word
echo 2. Apply formatting:
echo    - Title: Arial 16pt, Bold, Centered
echo    - Headings: Arial 14pt/12pt, Bold
echo    - Body: Times New Roman 12pt, Double-spaced
echo    - Margins: 1 inch all sides
echo 3. Update Table of Contents
echo 4. Add page numbers and headers
echo 5. Format tables and check spacing
echo.
echo For LaTeX Document:
echo 1. Review conference_paper.pdf
echo 2. Check all tables and figures
echo 3. Verify bibliography formatting
echo 4. Ensure all cross-references work
echo.

REM Optional cleanup
set /p cleanup="Clean auxiliary LaTeX files? (y/n): "
if /i "%cleanup%"=="y" (
    echo Cleaning auxiliary files...
    del /q *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fls *.fdb_latexmk *.synctex.gz 2>nul
    echo ✓ Cleanup complete!
)

echo.
echo Document compilation process completed!
echo See README_Document_Files.md for detailed instructions.
pause
