#!/bin/bash

# LaTeX Compilation Script for Conference Paper
# This script compiles the LaTeX document with proper bibliography handling

echo "Starting LaTeX compilation for conference paper..."

# Check if required files exist
if [ ! -f "conference_paper.tex" ]; then
    echo "Error: conference_paper.tex not found!"
    exit 1
fi

if [ ! -f "references.bib" ]; then
    echo "Error: references.bib not found!"
    exit 1
fi

# Clean previous compilation files
echo "Cleaning previous compilation files..."
rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fls *.fdb_latexmk *.synctex.gz

# First compilation
echo "First LaTeX compilation..."
pdflatex conference_paper.tex

# Bibliography compilation
echo "Compiling bibliography..."
bibtex conference_paper

# Second compilation (for bibliography)
echo "Second LaTeX compilation..."
pdflatex conference_paper.tex

# Third compilation (for cross-references)
echo "Third LaTeX compilation..."
pdflatex conference_paper.tex

# Check if PDF was created successfully
if [ -f "conference_paper.pdf" ]; then
    echo "✓ PDF compilation successful!"
    echo "Output file: conference_paper.pdf"
    
    # Display file size
    size=$(ls -lh conference_paper.pdf | awk '{print $5}')
    echo "File size: $size"
    
    # Count pages
    pages=$(pdfinfo conference_paper.pdf 2>/dev/null | grep Pages | awk '{print $2}')
    if [ ! -z "$pages" ]; then
        echo "Number of pages: $pages"
    fi
else
    echo "✗ PDF compilation failed!"
    echo "Check the log file for errors:"
    tail -20 conference_paper.log
    exit 1
fi

# Optional: Clean auxiliary files
read -p "Clean auxiliary files? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Cleaning auxiliary files..."
    rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fls *.fdb_latexmk *.synctex.gz
    echo "✓ Cleanup complete!"
fi

echo "LaTeX compilation process completed!"
