#!/bin/bash

# Word Document Conversion Script
# This script converts the markdown document to Microsoft Word format using pandoc

echo "Starting Word document conversion..."

# Check if pandoc is installed
if ! command -v pandoc &> /dev/null; then
    echo "Error: pandoc is not installed!"
    echo "Please install pandoc first:"
    echo "  macOS: brew install pandoc"
    echo "  Ubuntu: sudo apt-get install pandoc"
    echo "  Windows: Download from https://pandoc.org/installing.html"
    exit 1
fi

# Check if input file exists
if [ ! -f "conference_paper_word.md" ]; then
    echo "Error: conference_paper_word.md not found!"
    exit 1
fi

echo "Converting markdown to Word document..."

# Convert to Word with proper formatting
pandoc conference_paper_word.md \
    -o "Conference_Paper_Rural_Development.docx" \
    --from markdown \
    --to docx \
    --reference-doc=word_template.docx \
    --toc \
    --toc-depth=3 \
    --number-sections \
    --highlight-style=tango \
    --citeproc \
    --metadata title="Integrated Energy-Water-Health-Waste Management for Sustainable Rural Development" \
    --metadata subtitle="A Framework for SDG Integration and Decoupling Pathways" \
    --metadata author="Conference Paper Submission" \
    --metadata date="$(date +%Y-%m-%d)"

# Check if conversion was successful
if [ -f "Conference_Paper_Rural_Development.docx" ]; then
    echo "✓ Word document conversion successful!"
    echo "Output file: Conference_Paper_Rural_Development.docx"
    
    # Display file size
    size=$(ls -lh Conference_Paper_Rural_Development.docx | awk '{print $5}')
    echo "File size: $size"
    
    echo ""
    echo "📝 Manual formatting steps for Word document:"
    echo "1. Open the document in Microsoft Word"
    echo "2. Apply the following formatting:"
    echo "   - Title: Arial 16pt, Bold, Centered"
    echo "   - Section headings: Arial 14pt, Bold"
    echo "   - Subsection headings: Arial 12pt, Bold"
    echo "   - Body text: Times New Roman 12pt, Double-spaced"
    echo "   - Margins: 1 inch all sides"
    echo "   - Page numbers: Bottom right"
    echo "3. Update Table of Contents"
    echo "4. Check and format all tables"
    echo "5. Add running headers with paper title"
    echo "6. Review and adjust spacing"
    
else
    echo "✗ Word document conversion failed!"
    echo "Trying alternative conversion without template..."
    
    # Alternative conversion without template
    pandoc conference_paper_word.md \
        -o "Conference_Paper_Rural_Development.docx" \
        --from markdown \
        --to docx \
        --toc \
        --number-sections \
        --metadata title="Integrated Energy-Water-Health-Waste Management for Sustainable Rural Development"
    
    if [ -f "Conference_Paper_Rural_Development.docx" ]; then
        echo "✓ Alternative conversion successful!"
        echo "Note: Manual formatting will be required"
    else
        echo "✗ Conversion failed completely"
        exit 1
    fi
fi

echo ""
echo "📋 Additional files created:"
echo "- LaTeX source: conference_paper.tex"
echo "- Bibliography: references.bib"
echo "- LaTeX tables: latex_tables.tex"
echo "- Markdown source: conference_paper_word.md"
echo "- Word document: Conference_Paper_Rural_Development.docx"

echo ""
echo "🔧 To compile LaTeX version:"
echo "  chmod +x compile_latex.sh"
echo "  ./compile_latex.sh"

echo ""
echo "Document conversion process completed!"
